import { CollapsiblePanel } from '@/libs/ui/CollapsiblePanel/CollapsiblePanel';
import { Image } from '@mantine/core';
import { Flex } from '@/libs/ui/Flex/Flex';
import defaultProductImgUrl from '@/assets/images/default-product.png';
import { getPriceString } from '@/utils';
import { usePromotionData } from '@/libs/promotions/hooks/usePromotionData';
import { CheckoutPromoItem } from '../CheckoutPromoItem/CheckoutPromoItem';
import { CartVendorType } from '@/libs/cart/types';

type CheckoutVendorPanelProps = {
  vendor: CartVendorType;
};

export const CheckoutVendorPanel = ({ vendor }: CheckoutVendorPanelProps) => {
  const { id, name, imageUrl, items } = vendor;
  const { promotions, nonPromotionItems } = usePromotionData(items);

  return (
    <div key={id} className="mb-4">
      <CollapsiblePanel
        header={
          <Flex align="center" pr="5rem">
            <Image
              src={imageUrl}
              alt={name}
              fallbackSrc={defaultProductImgUrl}
              h={42}
              title={name}
            />
          </Flex>
        }
        content={
          <div className="grid divide-y divide-gray-200/80 text-sm">
            {nonPromotionItems.map(
              ({ quantity, product, subtotal, price, productOfferId }) => (
                <div
                  key={productOfferId}
                  className="grid grid-cols-[1fr_auto] gap-3 p-3"
                >
                  <span className="font-medium text-gray-900">
                    {product?.name}
                  </span>

                  <div className="flex">
                    <span className="text-gray-500">
                      Quantity:{' '}
                      <span className="font-medium text-gray-900">
                        {quantity}
                      </span>
                    </span>
                    <div className="divider-v"></div>

                    <span className="text-gray-500">
                      Unit:{' '}
                      <span className="font-medium text-gray-900">
                        {getPriceString(price)}
                      </span>
                    </span>
                    <div className="divider-v"></div>

                    <span className="text-gray-500">
                      Net Total:{' '}
                      <span className="font-medium text-gray-900">
                        {getPriceString(subtotal)}
                      </span>
                    </span>
                  </div>
                </div>
              ),
            )}
            {promotions.buy_x_get_y && (
              <CheckoutPromoItem promoItem={promotions.buy_x_get_y} />
            )}
          </div>
        }
        startOpen
      />
    </div>
  );
};
