import { Divider, Text } from '@mantine/core';
import { Flex } from '@/libs/ui/Flex/Flex';

import { useOrderDetails } from '../../services/useOrderDetails';
import styles from './OrderDetails.module.css';
import { OrderVendorPanel } from '../OrderVendorPanel/OrderVendorPanel';
import { ContentLoader } from '@/libs/ui/ContentLoader/ContentLoader';
import { VendorOrderSummary } from './VendorOrderSummary';
import { VendorPromotionGroupPanel } from '../VendorPromotionGroupPanel/VendorPromotionGroupPanel';
import {
  groupBuyXGetYItems,
  groupItemsByPromotion,
  vendorHasPromotions,
} from '@/libs/orders/utils/promotionGrouping';

interface OrderDetailsProps {
  id: string;
}
export const OrderDetails = ({ id }: OrderDetailsProps) => {
  const { order, isLoading } = useOrderDetails({ id });
  const { vendorOrders = [], promotions = [] } = order ?? {};

  if (isLoading || !order) {
    return <ContentLoader />;
  }

  return (
    <Flex p="1.5rem" direction="column" className={styles.container}>
      <Flex gap="xl">
        <div className="flex-grow">
          <Text c="#666" size="xs" mb="xs">
            Order Details
          </Text>
          <div className={`bg-[#F8FBFD] p-6 ${styles.info}`}>
            {vendorOrders.map(
              // TODO: Sync with BE missing fields
              (
                { items, vendor, totalTaxFee, shippingFee, totalPrice },
                index,
              ) => (
                <VendorOrderSummary
                  key={vendor.id}
                  vendor={vendor}
                  items={items}
                  totalTaxFee={totalTaxFee}
                  shippingFee={shippingFee}
                  totalPrice={totalPrice}
                  showDivider={index !== 0}
                />
              ),
            )}
          </div>
        </div>
      </Flex>
      <Divider my="xl" />
      {vendorOrders.map(({ vendor, items, totalPrice }) => {
        // Find the buy_x_get_y promotion
        const buyXGetYPromotion = promotions.find(
          (promo) => promo.type === 'buy_x_get_y',
        );

        // Check if this vendor has any promotion items
        const hasPromotions = vendorHasPromotions(items, buyXGetYPromotion);

        if (hasPromotions && buyXGetYPromotion) {
          // Group items by promotion for this vendor
          const buyXGetYPromotionGroup = groupBuyXGetYItems(
            items,
            buyXGetYPromotion,
          );

          if (!buyXGetYPromotionGroup) {
            return null;
          }

          return (
            <div key={vendor.id} className="mb-4">
              <VendorPromotionGroupPanel
                vendor={vendor}
                promotionGroups={buyXGetYPromotionGroups}
                totalPrice={+totalPrice}
                totalItems={items.length}
              />
            </div>
          );
        } else {
          // Use regular vendor panel for vendors without promotions
          return (
            <div key={vendor.id} className="mb-4">
              <OrderVendorPanel
                totalPrice={+totalPrice}
                totalItems={items.length}
                items={items}
                vendor={vendor}
              />
            </div>
          );
        }
      })}
    </Flex>
  );
};
