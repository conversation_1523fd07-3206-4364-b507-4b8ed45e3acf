import { Divider, Text } from '@mantine/core';
import { Flex } from '@/libs/ui/Flex/Flex';
import { getPriceString } from '@/utils';

interface VendorOrderSummaryProps {
  vendor: {
    id: string;
    name: string;
  };
  items: any[];
  totalTaxFee?: number | string | null;
  shippingFee?: number | string | null;
  totalPrice: number | string;
  showDivider?: boolean;
}

export const VendorOrderSummary = ({
  vendor,
  items,
  totalTaxFee,
  shippingFee,
  totalPrice,
  showDivider = false,
}: VendorOrderSummaryProps) => {
  return (
    <div>
      {showDivider && <Divider my="md" />}
      <Flex>
        <div className="flex-1">
          <Text c="#666" fw="400">
            Vendor
          </Text>
          <Text c="#333" fw="500">
            {vendor.name}
          </Text>
        </div>
        <Divider mx="md" orientation="vertical" />
        <div className="flex-1">
          <Text c="#666" fw="400">
            Line items
          </Text>
          <Text c="#333" fw="500">
            {items.length}
          </Text>
        </div>
        <Divider mx="md" orientation="vertical" />
        <div className="flex-1">
          <Text c="#666" fw="400">
            Taxes
          </Text>
          <Text c="#333" fw="500">
            {totalTaxFee ? getPriceString(+totalTaxFee) : '–'}
          </Text>
        </div>
        <Divider mx="md" orientation="vertical" />
        <div className="flex-1">
          <Text c="#666" fw="400">
            Shipping
          </Text>
          <Text c="#333" fw="500">
            {shippingFee ? getPriceString(+shippingFee) : '–'}
          </Text>
        </div>
        <Divider mx="md" orientation="vertical" />
        <div className="flex-1">
          <Text c="#666" fw="400">
            Vendor total
          </Text>
          <Text c="#333" fw="500">
            {getPriceString(+totalPrice)}
          </Text>
        </div>
      </Flex>
    </div>
  );
};
