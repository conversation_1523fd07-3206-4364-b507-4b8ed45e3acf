import { Divider, Text, Image } from '@mantine/core';
import { Flex } from '@/libs/ui/Flex/Flex';
import { getPriceString } from '@/utils';
import { CollapsiblePanel } from '@/libs/ui/CollapsiblePanel/CollapsiblePanel';
import { ProductCartHorizontal } from '@/libs/products/components/ProductCardHorizontal/ProductCartHorizontal';
import { getProductUrl } from '@/apps/shop/routes/utils';
import { Link } from 'react-router-dom';
import type {
  OrderHistoryDetailVendorOrderType,
  VendorPromotionGroups,
} from '@/libs/orders/types';

interface VendorPromotionGroupPanelProps {
  vendor: OrderHistoryDetailVendorOrderType['vendor'];
  promotionGroups: VendorPromotionGroups;
  totalPrice: number;
  totalItems: number;
}

export const VendorPromotionGroupPanel = ({
  vendor,
  promotionGroups,
  totalPrice,
  totalItems,
}: VendorPromotionGroupPanelProps) => {
  const { promotionGroups: promoGroups, nonPromotionItems } = promotionGroups;

  return (
    <CollapsiblePanel
      startOpen
      header={
        <Flex align="center" pr="5rem">
          <Image src={vendor.imageUrl} alt={vendor.name} h={42} />
          <div className="ml-4">
            <Text c="#333" size="md" fw="500">
              {getPriceString(totalPrice)}
              <Text c="#666" size="xs" span ml="0.2rem">
                ({totalItems} Items)
              </Text>
            </Text>
          </div>
        </Flex>
      }
      content={
        <Flex p="md" direction="column">
          {/* Render promotion groups */}
          {promoGroups.map((group, groupIndex) => (
            <div key={group.promotion.id}>
              {groupIndex > 0 && <Divider my="lg" />}

              {/* Promotion header */}
              <div className="mb-4">
                <div className="mb-1">
                  <span className="font-medium text-green-700">
                    Promotion •{' '}
                  </span>
                </div>
                <Text fw="500" c="#333" size="md" mb="xs">
                  {group.promotion.name}
                </Text>

                {/* Promotion summary */}
                <div className="rounded-lg bg-[#F8FBFD] p-4">
                  <Flex justify="space-between" align="center">
                    <div>
                      <Text size="sm" c="#666">
                        You got total of{' '}
                        <Text span fw="500" c="#333">
                          {group.promotionData.paidItemsQty +
                            group.promotionData.freeItemsQty}
                        </Text>{' '}
                        products ({group.promotionData.paidItemsQty} paid +{' '}
                        {group.promotionData.freeItemsQty} free)
                      </Text>
                    </div>
                    <div className="text-right">
                      <Text size="sm" c="#666">
                        Savings:{' '}
                        {getPriceString(
                          group.promotionData.subtotalAllItems -
                            group.promotionData.subtotalPaidItems,
                        )}
                      </Text>
                    </div>
                  </Flex>
                </div>
              </div>

              {/* Promotion items */}
              {group.items.map((item, itemIndex) => (
                <div key={item.id}>
                  {itemIndex !== 0 && <Divider my="md" />}
                  <ProductCartHorizontal
                    product={item.product}
                    productOfferId={item.productOfferId}
                    content={
                      <Flex>
                        <div className="ml-4">
                          <Text
                            fw="500"
                            size="xs"
                            mb="0.1rem"
                            c="rgba(102, 102, 102, 0.70)"
                          >
                            Order ID: {item.orderNumber}
                          </Text>
                          <Link
                            to={getProductUrl(
                              item.product.id,
                              item.productOfferId,
                            )}
                            className="no-underline hover:underline"
                          >
                            <Text fw="500" c="#333">
                              {item.product.name}
                            </Text>
                          </Link>
                          <Flex mt="lg" direction="column" gap="xs">
                            <>
                              {/* Calculate paid and free quantities for this specific item */}
                              {(() => {
                                // For buy_x_get_y promotions, calculate per-item breakdown
                                const buyQuantity = 1; // From promotion rules
                                const getQuantity = 1; // From promotion benefits
                                const groupSize = buyQuantity + getQuantity;
                                const completeGroups = Math.floor(
                                  item.quantity / groupSize,
                                );
                                const remainingItems =
                                  item.quantity % groupSize;
                                const itemPaidQty =
                                  completeGroups * buyQuantity + remainingItems;
                                const itemFreeQty =
                                  completeGroups * getQuantity;

                                return (
                                  <>
                                    {/* Paid quantity row */}
                                    <Flex>
                                      <Text
                                        py="sm"
                                        pr="sm"
                                        miw="6rem"
                                        size="xs"
                                        c="#666"
                                      >
                                        Quantity:{' '}
                                        <Text c="#333" fw="700" span>
                                          {itemPaidQty}
                                        </Text>
                                      </Text>
                                      <Divider orientation="vertical" />
                                      <Text p="sm" size="xs" c="#666">
                                        Price:{' '}
                                        <Text c="#333" fw="700" span>
                                          {getPriceString(
                                            parseFloat(item.unitPrice),
                                          )}
                                        </Text>
                                      </Text>
                                      <Divider orientation="vertical" />
                                      <Text p="sm" size="xs" c="#666">
                                        Net Total:{' '}
                                        <Text c="#333" fw="700" span>
                                          {getPriceString(
                                            itemPaidQty *
                                              parseFloat(item.unitPrice),
                                          )}
                                        </Text>
                                      </Text>
                                    </Flex>

                                    {/* Free quantity row */}
                                    {itemFreeQty > 0 && (
                                      <Flex>
                                        <Text
                                          py="sm"
                                          pr="sm"
                                          miw="6rem"
                                          size="xs"
                                          c="#666"
                                        >
                                          Quantity:{' '}
                                          <Text c="#333" fw="700" span>
                                            {itemFreeQty}
                                          </Text>
                                        </Text>
                                        <Divider orientation="vertical" />
                                        <Text p="sm" size="xs" c="#666">
                                          Price:{' '}
                                          <Text c="green" fw="700" span>
                                            Free
                                          </Text>
                                        </Text>
                                        <Divider orientation="vertical" />
                                        <Text p="sm" size="xs" c="#666">
                                          Net Total:{' '}
                                          <Text c="green" fw="700" span>
                                            {getPriceString(0)}
                                          </Text>
                                        </Text>
                                      </Flex>
                                    )}
                                  </>
                                );
                              })()}
                            </>
                          </Flex>
                        </div>
                      </Flex>
                    }
                  />
                </div>
              ))}
            </div>
          ))}

          {/* Render non-promotion items */}
          {nonPromotionItems.length > 0 && (
            <div>
              {promoGroups.length > 0 && <Divider my="lg" />}
              {nonPromotionItems.map((item, index) => (
                <div key={item.id}>
                  {index !== 0 && <Divider my="md" />}
                  <ProductCartHorizontal
                    product={item.product}
                    productOfferId={item.productOfferId}
                    content={
                      <Flex>
                        <div className="ml-4">
                          <Text
                            fw="500"
                            size="xs"
                            mb="0.1rem"
                            c="rgba(102, 102, 102, 0.70)"
                          >
                            Order ID: {item.orderNumber}
                          </Text>
                          <Link
                            to={getProductUrl(
                              item.product.id,
                              item.productOfferId,
                            )}
                            className="no-underline hover:underline"
                          >
                            <Text fw="500" c="#333">
                              {item.product.name}
                            </Text>
                          </Link>
                          <Flex mt="lg">
                            <Text py="sm" pr="sm" miw="6rem" size="xs" c="#666">
                              Quantity:{' '}
                              <Text c="#333" fw="700" span>
                                {item.quantity}
                              </Text>
                            </Text>
                            <Divider orientation="vertical" />
                            <Text p="sm" size="xs" c="#666">
                              Price:{' '}
                              <Text c="#333" fw="700" span>
                                {getPriceString(parseFloat(item.unitPrice))}
                              </Text>
                            </Text>
                            <Divider orientation="vertical" />
                            <Text p="sm" size="xs" c="#666">
                              Net Total:{' '}
                              <Text c="#333" fw="700" span>
                                {getPriceString(parseFloat(item.totalPrice))}
                              </Text>
                            </Text>
                          </Flex>
                        </div>
                      </Flex>
                    }
                  />
                </div>
              ))}
            </div>
          )}
        </Flex>
      }
    />
  );
};
