{"id": "9f95cb54-d35b-49d5-91cc-a40805a8f350", "orderNumber": "HFO008594", "date": "2025-08-08", "totalPrice": "62.85", "status": "PENDING", "downloadChecklistUrl": "https://staging.services.highfive.vet/api/orders/9f95cb54-d35b-49d5-91cc-a40805a8f350/download-checklist?signature=dc15a2e31aa5db33b0bbff8e94923670ba2fd76cbe5189350804fe0141c6cc7b", "downloadInvoicesUrl": null, "vendorOrders": [{"id": "9f95cb54-d555-41cd-ad14-0a1bd29be31e", "vendor": {"id": "9d7559b3-2608-4f66-a75a-1f1b77cd472d", "name": "<PERSON><PERSON>", "imageUrl": "https://staging.services.highfive.vet/storage/vendor-images/zoetis.png"}, "items": [{"id": "019889b6-9bbf-73df-9d61-3255bb6dab6c", "orderNumber": "HFO008594", "unitPrice": "21.30", "quantity": 1, "taxFee": null, "totalPrice": "21.30", "status": "PLACEMENT_FAILED", "productOfferId": "9d757019-7202-48c6-ac43-5f792582af68", "product": {"id": "85009b46-2e26-4be2-b078-cfaa9997f550", "name": "Clavamox (Amoxicillin / Clavulanate) Drops for Veterinary Oral Suspension, 15mL", "imageUrl": "https://ws.mwiah.com/media/image?id=ec8dd978-232f-4b50-bd0a-f6dca46574d7"}}, {"id": "019889b6-9bbd-72d6-8827-0fb1ebdcd0d5", "orderNumber": "HFO008594", "unitPrice": "41.55", "quantity": 1, "taxFee": null, "totalPrice": "41.55", "status": "PLACEMENT_FAILED", "productOfferId": "9d7581c7-e947-40f0-adb7-471859462816", "product": {"id": "01969b4c-645d-73bd-9e1d-2d31da2c7728", "name": "Simparica Chewables for Dogs 2.8 to 5.5 Pounds, Gold Label (3 Dose x 10)", "imageUrl": "https://ws.mwiah.com/media/image?id=9916dd3f-cc6c-4a81-836f-65bf913e7c69"}}], "totalPrice": "62.85", "totalTaxFee": null, "shippingFee": null}], "promotions": [{"id": "019889b6-a60d-71c4-ad1e-38138975b635", "name": "Buy 1 Simparica 3 x 5mg Gold 2.8 - 5.5lbs, get 1 for Free", "type": "buy_x_get_y", "triggeringItems": [{"productOfferId": "9d7581c7-e947-40f0-adb7-471859462816", "productId": "01969b4c-645d-73bd-9e1d-2d31da2c7728", "quantity": 1}], "appliedRules": [{"ruleId": "0197d1ad-c40b-70dc-aa3f-d4376f3d426f", "priority": 1, "conditions": [{"id": "01986615-161a-723e-b6d3-ba759db21157", "type": "minimum_quantity", "description": "Minimum Quantity: 1", "config": {"quantity": "1"}}], "actions": [{"id": "01986615-3cd4-728a-a048-53c2d4c63d86", "type": "give_free_product", "description": "Give Free Product", "config": {"message": null, "quantity": "1", "freeProductOfferId": "9d7581c7-e947-40f0-adb7-471859462816"}}]}], "appliedBenefits": [{"type": "give_free_product", "productOfferId": "9d7581c7-e947-40f0-adb7-471859462816", "quantity": "1", "message": null}]}, {"id": "019889b6-a61b-73f9-9eef-d13c1813638d", "name": "SIMPARICA (sarolaner)", "type": "rebate", "triggeringItems": [{"productOfferId": "9d792652-d506-43eb-b27b-b3c1fca8faca", "productId": "01969b4c-645d-73bd-9e1d-2d31da2c7728", "quantity": 1}], "appliedRules": [{"ruleId": "01968c2d-a028-7058-9d8c-9f44a9a7ebf6", "priority": 1, "conditions": [], "actions": [{"id": "01968c2d-bcf0-728d-a9e4-fcfc4ba6037d", "type": "update_rebate_estimate", "description": "Update Rebate Estimate", "config": {"rebatePercent": 10}}]}], "appliedBenefits": [{"type": "rebate", "percentage": 10, "description": "10% rebate on qualifying purchases"}]}]}