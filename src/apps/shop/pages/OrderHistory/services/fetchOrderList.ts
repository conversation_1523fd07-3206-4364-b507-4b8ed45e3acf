import { get } from '@/libs/utils/api';
import type { OrderHistoryItemType } from '@/libs/orders/types';
import type { GetDataWithPagination } from '@/types/utility';
import { OrderListSearchParamsType } from './useOrderListSearchParams';

interface fetchOrderListProps {
  searchParams: OrderListSearchParamsType;
  beforeStart?: VoidFunction;
  onSuccess?: (data: { orders: OrderHistoryItemType[]; total: number }) => void;
  onError?: VoidFunction;
  afterDone?: VoidFunction;
}

// TODO: Make it generic and re-useable
const parseQueryParams = (searchParams: OrderListSearchParamsType) => {
  const {
    query,
    dateFromFilter,
    dateToFilter,
    page,
    perPage,
    // TODO: Add sort
    // sort,
  } = searchParams;

  let queryString = '';
  queryString += query ? `filter[search]=${query}` : '';
  queryString += dateFromFilter ? `&filter[date_from]=${dateFromFilter}` : '';
  queryString += dateToFilter ? `&filter[date_to]=${dateToFilter}` : '';
  queryString += page ? `&page[number]=${page}` : '';
  queryString += perPage ? `&page[size]=${perPage}` : '';

  return queryString;
};

export const fetchOrderList = async ({
  searchParams,
  beforeStart = () => {},
  onSuccess = () => {},
  onError = () => {},
  afterDone = () => {},
}: fetchOrderListProps) => {
  beforeStart();

  const queryString = parseQueryParams(searchParams);
  try {
    const response = {
      data: [
        {
          id: '9f95cb54-d35b-49d5-91cc-a40805a8f350',
          orderNumber: 'HFO008594',
          date: '2025-08-08',
          totalPrice: '62.85',
          status: 'PENDING',
          downloadChecklistUrl:
            'https://staging.services.highfive.vet/api/orders/9f95cb54-d35b-49d5-91cc-a40805a8f350/download-checklist?signature=dc15a2e31aa5db33b0bbff8e94923670ba2fd76cbe5189350804fe0141c6cc7b',
          downloadInvoicesUrl: null,
          itemsCount: 2,
          vendorsCount: 1,
        },
        {
          id: '9f95c2d2-2817-418f-b8b8-369aa97bda9b',
          orderNumber: 'HFO008593',
          date: '2025-08-08',
          totalPrice: '104.40',
          status: 'PENDING',
          downloadChecklistUrl:
            'https://staging.services.highfive.vet/api/orders/9f95c2d2-2817-418f-b8b8-369aa97bda9b/download-checklist?signature=0030a856a60ab258e1a8048c7333a4b3bf08d015027bd65ac4b4f10d66eb6c74',
          downloadInvoicesUrl: null,
          itemsCount: 2,
          vendorsCount: 1,
        },
        {
          id: '9f95bbee-6fdf-4394-8186-15d55cf19028',
          orderNumber: 'HFO008592',
          date: '2025-08-08',
          totalPrice: '166.20',
          status: 'PENDING',
          downloadChecklistUrl:
            'https://staging.services.highfive.vet/api/orders/9f95bbee-6fdf-4394-8186-15d55cf19028/download-checklist?signature=a9d8d4f871008aaca23daa5aa7bc284866f85a4eaa90d9fd9167e8b938a18b83',
          downloadInvoicesUrl: null,
          itemsCount: 1,
          vendorsCount: 1,
        },
        {
          id: '9f0987f0-7b69-4086-ae83-8eb1dfefb246',
          orderNumber: 'IOH000140',
          date: '2024-11-24',
          totalPrice: '110.00',
          status: 'DELIVERED',
          downloadChecklistUrl:
            'https://staging.services.highfive.vet/api/orders/9f0987f0-7b69-4086-ae83-8eb1dfefb246/download-checklist?signature=586fdd2dafe79b5b7245a47ada3fa449605488ace2394a43ea3daf6749239dce',
          downloadInvoicesUrl: null,
          itemsCount: 1,
          vendorsCount: 1,
        },
        {
          id: '9f0987f0-898a-4896-a5d0-867a473f3771',
          orderNumber: 'IOH000141',
          date: '2024-11-18',
          totalPrice: '27.48',
          status: 'DELIVERED',
          downloadChecklistUrl:
            'https://staging.services.highfive.vet/api/orders/9f0987f0-898a-4896-a5d0-867a473f3771/download-checklist?signature=a0a388cf586bf1acee020cac1be32c8b614c88fdb70fd0bb6bf4aeb9ec618b42',
          downloadInvoicesUrl: null,
          itemsCount: 1,
          vendorsCount: 1,
        },
      ],
      links: [
        {
          url: null,
          label: '&laquo; Previous',
          active: false,
        },
        {
          url: 'https://staging.services.highfive.vet/api/orders?page%5Bsize%5D=5&page%5Bnumber%5D=1',
          label: '1',
          active: true,
        },
        {
          url: 'https://staging.services.highfive.vet/api/orders?page%5Bsize%5D=5&page%5Bnumber%5D=2',
          label: '2',
          active: false,
        },
        {
          url: 'https://staging.services.highfive.vet/api/orders?page%5Bsize%5D=5&page%5Bnumber%5D=2',
          label: 'Next &raquo;',
          active: false,
        },
      ],
      meta: {
        currentPage: 1,
        firstPageUrl:
          'https://staging.services.highfive.vet/api/orders?page%5Bsize%5D=5&page%5Bnumber%5D=1',
        from: 1,
        lastPage: 2,
        lastPageUrl:
          'https://staging.services.highfive.vet/api/orders?page%5Bsize%5D=5&page%5Bnumber%5D=2',
        nextPageUrl:
          'https://staging.services.highfive.vet/api/orders?page%5Bsize%5D=5&page%5Bnumber%5D=2',
        path: 'https://staging.services.highfive.vet/api/orders',
        perPage: 5,
        prevPageUrl: null,
        to: 5,
        total: 9,
      },
    };

    onSuccess({
      orders: response.data,
      total: response.meta.total,
    });
  } catch {
    onError();
  }

  afterDone();
};
