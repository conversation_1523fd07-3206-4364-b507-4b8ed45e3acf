import { groupItemsByPromotion, vendorHasPromotions } from '../promotionGrouping';
import type {
  OrderHistoryDetailItemType,
  OrderHistoryPromotion,
} from '../../types';

// Mock data based on the actual cartMock.json
const mockItem: OrderHistoryDetailItemType = {
  id: '0198898f-2f39-702f-9b14-d186f8cf3aeb',
  orderNumber: 'HFO008592',
  unitPrice: '41.55',
  quantity: 4,
  taxFee: null,
  totalPrice: '166.20',
  status: 'PENDING',
  productOfferId: '9d7581c7-e947-40f0-adb7-471859462816',
  product: {
    id: '01969b4c-645d-73bd-9e1d-2d31da2c7728',
    name: 'Simparica Chewables for Dogs 2.8 to 5.5 Pounds, Gold Label (3 Dose x 10)',
    imageUrl: 'https://ws.mwiah.com/media/image?id=9916dd3f-cc6c-4a81-836f-65bf913e7c69',
  },
};

const mockBuyXGetYPromotion: OrderHistoryPromotion = {
  id: '0198898f-3abe-735f-a0e0-798e4b965264',
  name: 'Buy 1 Simparica 3 x 5mg Gold 2.8 - 5.5lbs, get 1 for Free',
  type: 'buy_x_get_y',
  triggeringItems: [
    {
      productOfferId: '9d7581c7-e947-40f0-adb7-471859462816',
      productId: '01969b4c-645d-73bd-9e1d-2d31da2c7728',
      quantity: 4,
    },
  ],
  appliedRules: [],
  appliedBenefits: [
    {
      type: 'give_free_product',
      productOfferId: null,
      quantity: '1',
      message: null,
    },
  ],
};

const mockNonMatchingPromotion: OrderHistoryPromotion = {
  id: '0198898f-3ab7-71db-922a-35a767666760',
  name: 'Different Product Promotion',
  type: 'buy_x_get_y',
  triggeringItems: [
    {
      productOfferId: '9d792652-d506-43eb-b27b-b3c1fca8faca', // Different productOfferId
      productId: '01969b4c-645d-73bd-9e1d-2d31da2c7728',
      quantity: 4,
    },
  ],
  appliedRules: [],
  appliedBenefits: [
    {
      type: 'give_free_product',
      productOfferId: null,
      quantity: '1',
      message: null,
    },
  ],
};

describe('promotionGrouping', () => {
  describe('vendorHasPromotions', () => {
    it('should return true when vendor has items matching promotion', () => {
      const result = vendorHasPromotions([mockItem], mockBuyXGetYPromotion);
      expect(result).toBe(true);
    });

    it('should return false when vendor has no items matching promotion', () => {
      const result = vendorHasPromotions([mockItem], mockNonMatchingPromotion);
      expect(result).toBe(false);
    });

    it('should return false when no promotion exists', () => {
      const result = vendorHasPromotions([mockItem], undefined);
      expect(result).toBe(false);
    });
  });

  describe('groupItemsByPromotion', () => {
    it('should group items by matching promotion', () => {
      const result = groupItemsByPromotion([mockItem], mockBuyXGetYPromotion);

      expect(result.promotionGroups).toHaveLength(1);
      expect(result.promotionGroups[0].promotion.id).toBe(mockBuyXGetYPromotion.id);
      expect(result.promotionGroups[0].items).toHaveLength(1);
      expect(result.promotionGroups[0].items[0].id).toBe(mockItem.id);
      expect(result.nonPromotionItems).toHaveLength(0);
    });

    it('should put non-matching items in nonPromotionItems', () => {
      const result = groupItemsByPromotion([mockItem], mockNonMatchingPromotion);

      expect(result.promotionGroups).toHaveLength(0);
      expect(result.nonPromotionItems).toHaveLength(1);
      expect(result.nonPromotionItems[0].id).toBe(mockItem.id);
    });

    it('should handle undefined promotion', () => {
      const result = groupItemsByPromotion([mockItem], undefined);

      expect(result.promotionGroups).toHaveLength(0);
      expect(result.nonPromotionItems).toHaveLength(1);
      expect(result.nonPromotionItems[0].id).toBe(mockItem.id);
    });

    it('should calculate promotion data correctly for buy_x_get_y', () => {
      const result = groupItemsByPromotion([mockItem], mockBuyXGetYPromotion);

      const promotionData = result.promotionGroups[0].promotionData;
      expect(promotionData.promotion.type).toBe('buy_x_get_y');
      // For "buy 1 get 1 free" with 4 items: 2 groups of (1 paid + 1 free) = 2 paid + 2 free
      expect(promotionData.paidItemsQty).toBe(2);
      expect(promotionData.freeItemsQty).toBe(2);
      expect(promotionData.subtotalPaidItems).toBe(2 * 41.55); // 2 paid items * unit price
      expect(promotionData.subtotalAllItems).toBe(4 * 41.55); // All 4 items * unit price
    });
  });
});
