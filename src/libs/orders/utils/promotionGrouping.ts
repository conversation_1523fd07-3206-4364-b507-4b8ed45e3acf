/* eslint-disable prettier/prettier */
import type {
  OrderHistoryDetailItemType,
  OrderHistoryPromotion,
  OrderHistoryPromotionAppliedBenefit,
  VendorPromotionGroups,
} from '../types';


const getMinimumQuantity = (promotion: OrderHistoryPromotion) => {
  const minimumQuantityCondition = promotion.appliedRules
    .flatMap((rule) => rule.conditions)
    .find((condition) => condition.type === 'minimum_quantity');

  return minimumQuantityCondition ? parseInt(minimumQuantityCondition.config.value) : 1;
};

export const getItemDetails = ({
  item,
  promotion,
  freeBenefit,
}: {
  item: OrderHistoryDetailItemType;
  promotion: OrderHistoryPromotion;
  freeBenefit: OrderHistoryPromotionAppliedBenefit;
}) => {
  const triggerQty = getMinimumQuantity(promotion);
  const freeQtyPerTrigger = parseInt(freeBenefit.quantity);
  const triggers = Math.floor(item.quantity / triggerQty);
  const freeItemsQty = triggers * freeQtyPerTrigger;

  return {
    subtotalPaidItems: item.totalPrice,
    subtotalAllItems: +item.totalPrice + (freeItemsQty * +item.unitPrice),
    paidItemsQty: item.quantity,
    freeItemsQty,
    freeOffer: freeBenefit.freeProductOffer,
  };
};
const offerIds = ({ productOfferId }: { productOfferId: string }) =>
  productOfferId;
export function groupBuyXGetYItems(
  items: OrderHistoryDetailItemType[],
  buyXGetYPromotion: OrderHistoryPromotion,
): VendorPromotionGroups | null {
  const triggeredOfferIds = buyXGetYPromotion.triggeringItems.map(offerIds);
  const { triggeredItems, untriggeredItems } = items.reduce(
    (acc, item) => {
      const itemsType = triggeredOfferIds.includes(item.productOfferId)
        ? 'triggeredItems'
        : 'untriggeredItems';
      return {
        ...acc,
        [itemsType]: [...acc[itemsType], item],
      };
    },
    {
      triggeredItems: [] as OrderHistoryDetailItemType[],
      untriggeredItems: [] as OrderHistoryDetailItemType[],
    },
  );

  const freeBenefit = buyXGetYPromotion.appliedBenefits.find(
    (benefit) => benefit.type === 'give_free_product',
  );

  if (!freeBenefit) return null;

  const data = triggeredItems.reduce(
    (acc, item) => {
      const detail = getItemDetails({ item, promotion: buyXGetYPromotion, freeBenefit });
      return {
        subtotalPaidItems: acc.subtotalPaidItems + detail.subtotalPaidItems,
        subtotalAllItems: acc.subtotalAllItems + detail.subtotalAllItems,
        paidItemsQty: acc.paidItemsQty + detail.paidItemsQty,
        freeItemsQty: acc.freeItemsQty + detail.freeItemsQty,
        items: [...acc.items, {
          ...item,
          subtotalPaidItems: acc.subtotalPaidItems + detail.subtotalPaidItems,
          subtotalAllItems: acc.subtotalAllItems + detail.subtotalAllItems,
          paidItemsQty: acc.paidItemsQty + detail.paidItemsQty,
          freeItemsQty: acc.freeItemsQty + detail.freeItemsQty,
          freeOffer
        }],
      };
    },
    {
      item: [] as OrderHistoryDetailItemType[],
      subtotalPaidItems: 0,
      subtotalAllItems: 0,
      paidItemsQty: 0,
      freeItemsQty: 0,
    },
  );

  return {
    triggeredItems: {
      promotion: buyXGetYPromotion,
      items: triggeredItems,
    },
    untriggeredItems,
  };
}

export function vendorHasPromotions(
  items: OrderHistoryDetailItemType[],
  promotion: OrderHistoryPromotion | undefined,
): boolean {
  if (!promotion || promotion.type !== 'buy_x_get_y') {
    return false;
  }

  return promotion.triggeringItems.some((triggeringItem) =>
    items.some((item) => item.productOfferId === triggeringItem.productOfferId),
  );
}
